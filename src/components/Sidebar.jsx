import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { MacOSCard, MacOSCardContent } from './ui/macos-card';
import { Home, Store, Settings, Menu } from 'lucide-react';
import { Typography, slideIn } from '../utils/typography';

const Sidebar = ({ isVisible, onToggle }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const isActive = (path) => {
    // For app routes, check if we came from a specific tab
    if (location.pathname.startsWith('/app/')) {
      // Check if there's a referrer state or use sessionStorage to remember the source
      const sourceTab = sessionStorage.getItem('sourceTab') || '/';
      return sourceTab === path;
    }
    return location.pathname === path;
  };

  return (
    <motion.div
      className="h-full bg-macos-surface/80 backdrop-blur-xl border-r border-macos-border/50 flex flex-col"
      initial="initial"
      animate="animate"
      variants={slideIn}
    >
      {/* Navigation */}
      <div className="px-4 pt-12 pb-4 space-y-3">
        <Button
          variant="ghost"
          className={`w-full justify-start h-12 rounded-xl focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:outline-none transition-all duration-200 focus:no-underline ${
            isActive('/')
              ? 'bg-system-blue hover:bg-system-blue/90 text-white shadow-sm'
              : 'hover:bg-macos-elevated text-macos-text-primary'
          }`}
          onClick={() => navigate('/')}
          data-testid="nav-home"
        >
          <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
            isActive('/') ? 'bg-white/20' : 'bg-blue-100'
          }`}>
            <Home className={`h-4 w-4 ${isActive('/') ? 'text-white' : 'text-blue-600'}`} />
          </div>
          <span className={`${Typography.button} ${isActive('/') ? 'text-white' : ''}`}>Home</span>
        </Button>

        <Button
          variant="ghost"
          className={`w-full justify-start h-12 rounded-xl focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:outline-none transition-all duration-200 ${
            isActive('/store')
              ? 'bg-system-purple hover:bg-system-purple/90 text-white shadow-sm'
              : 'hover:bg-macos-elevated text-macos-text-primary'
          }`}
          onClick={() => navigate('/store')}
          data-testid="nav-store"
        >
          <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
            isActive('/store') ? 'bg-white/20' : 'bg-purple-100'
          }`}>
            <Store className={`h-4 w-4 ${isActive('/store') ? 'text-white' : 'text-purple-600'}`} />
          </div>
          <span className={`${Typography.button} ${isActive('/store') ? 'text-white' : ''}`}>Tool Store</span>
        </Button>

        <Button
          variant="ghost"
          className={`w-full justify-start h-12 rounded-xl focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:outline-none transition-all duration-200 ${
            isActive('/preferences')
              ? 'bg-system-green hover:bg-system-green/90 text-white shadow-sm'
              : 'hover:bg-macos-elevated text-macos-text-primary'
          }`}
          onClick={() => navigate('/preferences')}
          data-testid="nav-preferences"
        >
          <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
            isActive('/preferences') ? 'bg-white/20' : 'bg-green-100'
          }`}>
            <Settings className={`h-4 w-4 ${isActive('/preferences') ? 'text-white' : 'text-green-600'}`} />
          </div>
          <span className={`${Typography.button} ${isActive('/preferences') ? 'text-white' : ''}`}>Preferences</span>
        </Button>

        <Button
          variant="ghost"
          className={`w-full justify-start h-12 rounded-xl focus:outline-none focus:ring-0 focus-visible:ring-0 focus-visible:outline-none transition-all duration-200 ${
            isActive('/debug-database')
              ? 'bg-system-orange hover:bg-system-orange/90 text-white shadow-sm'
              : 'hover:bg-macos-elevated text-macos-text-primary'
          }`}
          onClick={() => navigate('/debug-database')}
          data-testid="nav-debug-database"
        >
          <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 ${
            isActive('/debug-database') ? 'bg-white/20' : 'bg-orange-100'
          }`}>
            <Database className={`h-4 w-4 ${isActive('/debug-database') ? 'text-white' : 'text-orange-600'}`} />
          </div>
          <span className={`${Typography.button} ${isActive('/debug-database') ? 'text-white' : ''}`}>Debug Database</span>
        </Button>
      </div>

      {/* User Profile */}
      <div className="mt-auto p-4">
        <MacOSCard className="shadow-sm cursor-pointer hover:bg-macos-elevated transition-colors" onClick={() => navigate('/preferences')}>
          <MacOSCardContent className="p-3">
            <div className="flex items-start space-x-3">
              <img
                src="https://cdn.jsdelivr.net/gh/alohe/memojis/png/vibrent_3.png"
                alt="Amin Pial"
                className="w-10 h-10 rounded-full flex-shrink-0"
              />
              <div className="flex-1 min-w-0">
                <p className={`${Typography.body} font-semibold truncate`}>
                  Amin Pial
                </p>
                <p className={`${Typography.caption} truncate mb-2`}>
                  <EMAIL>
                </p>
              </div>
            </div>
            <div className="bg-macos-elevated rounded-lg p-2 mt-2">
              <p className={`${Typography.footnote} font-medium mb-1`}>
                License No:
              </p>
              <p className={`${Typography.footnote} font-mono break-all`}>
                pial-2025-06-03-4587X2
              </p>
            </div>
          </MacOSCardContent>
        </MacOSCard>
      </div>
    </motion.div>
  );
};

export default Sidebar;
