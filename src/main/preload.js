const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // System methods
  platform: process.platform,
  toggleDevTools: () => ipcRenderer.invoke('toggle-devtools'),

  // Utility management methods (refactored with database)
  getInstalledUtilities: () => ipcRenderer.invoke('get-installed-utilities'),
  installUtility: (downloadUrl, apiAppData) => ipcRenderer.invoke('install-utility', downloadUrl, apiAppData),
  uninstallUtility: (utilityId) => ipcRenderer.invoke('uninstall-utility', utilityId),
  runUtility: (utilityId, fileData, config) => ipcRenderer.invoke('run-utility', utilityId, fileData, config),
  isUtilityInstalled: (appId) => ipcRenderer.invoke('is-utility-installed', appId),
  getUtilityInfo: (appId) => ipcRenderer.invoke('get-utility-info', appId),
  getAllApps: (options) => ipc<PERSON>enderer.invoke('get-all-apps', options),
  searchApps: (query) => ipcRenderer.invoke('search-apps', query),
  getAppStats: () => ipcRenderer.invoke('get-app-stats'),
  getAnalyticsSummary: (timeRange) => ipcRenderer.invoke('get-analytics-summary', timeRange),
  getRecentLogs: (limit) => ipcRenderer.invoke('get-recent-logs', limit),

  // File management methods
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, data, options) => ipcRenderer.invoke('write-file', filePath, data, options),
  openFolder: (folderPath) => ipcRenderer.invoke('open-folder', folderPath),

  // WASM execution methods
  executeWasm: (wasmPath, functionName, ...args) => ipcRenderer.invoke('execute-wasm', wasmPath, functionName, ...args),
  processFileWasm: (wasmPath, fileData, config) => ipcRenderer.invoke('process-file-wasm', wasmPath, fileData, config),

  // Python execution methods
  executePython: (pythonPath, functionName, ...args) => ipcRenderer.invoke('execute-python', pythonPath, functionName, ...args),
  processFilePython: (pythonPath, fileData, config) => ipcRenderer.invoke('process-file-python', pythonPath, fileData, config),
  processFilesPython: (pythonPath, filesData, config) => ipcRenderer.invoke('process-files-python', pythonPath, filesData, config),

  getUserDataPath: () => ipcRenderer.invoke('get-user-data-path')
});
