# 🦆 FileDuck SDK Design Guideline v2.0

## 🎯 Overview

This document provides comprehensive guidelines for building FileDuck utility apps. All utilities must follow this exact specification to ensure compatibility with the FileDuck desktop application.

## 🏗️ Architecture Requirements

### Core Technology Stack
- **Runtime**: Python 3.x with spawn-based execution
- **UI Framework**: React with inline JSX in config.json
- **Styling**: Tailwind CSS + Apple Design Guidelines
- **Components**: Existing FileDuck ShadCN component library
- **Animation**: Framer Motion for smooth interactions
- **File Handling**: CLI-based with stdin/stdout communication

### Project Structure
```
/apps/{utility-name}/
├── dist/
│   ├── main.py           # Core Python logic
│   ├── config.json       # App metadata + UI definition
│   ├── tests.py          # Unit tests
│   └── requirements.txt  # Python dependencies
└── {utility-name}.zip    # Final distribution package
```

## 📋 File Specifications

### 1. main.py Requirements

#### Core Structure
```python
#!/usr/bin/env python3
"""
Utility Name - Brief Description
"""
import sys
import json
import os
from pathlib import Path

def main_function(input_data, config_options=None):
    """
    Core utility function
    
    Args:
        input_data: File data from FileDuck
        config_options: User configuration options
    
    Returns:
        dict: Result with success status and output info
    """
    try:
        # Implementation logic here
        
        # Create output in /converted/ directory
        converted_dir = Path.cwd() / "converted"
        converted_dir.mkdir(exist_ok=True)
        
        return {
            'success': True,
            'output_file': str(output_path),
            'message': 'Operation completed successfully'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'message': f'Operation failed: {str(e)}'
        }

def main():
    """Main entry point for FileDuck communication"""
    try:
        # Read input from stdin (JSON format)
        input_data = json.load(sys.stdin)
        
        files = input_data.get('files', [])
        config = input_data.get('config', {})
        
        if not files:
            result = {
                'success': False,
                'error': 'No files provided',
                'message': 'Please select files to process'
            }
            print(json.dumps(result))
            return
        
        results = []
        for file_info in files:
            file_path = file_info.get('path') or file_info.get('filePath')
            if file_path:
                result = main_function(file_path, config)
                results.append({
                    'input_file': file_path,
                    'result': result
                })
        
        output = {
            'success': True,
            'results': results,
            'total_files': len(results),
            'successful_operations': sum(1 for r in results if r['result']['success'])
        }
        
        print(json.dumps(output))
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'message': f'Operation failed: {str(e)}'
        }
        print(json.dumps(error_result))

if __name__ == '__main__':
    main()
```

#### Python Code Standards
- ✅ **CLI Only**: No GUI prompts, no `input()` calls
- ✅ **JSON Communication**: stdin/stdout for FileDuck integration
- ✅ **Error Handling**: Comprehensive try-catch with clear messages
- ✅ **File Paths**: Accept file paths, never embed file content
- ✅ **Output Location**: Save to `/converted/` directory in project root
- ✅ **Logging**: Use print() for JSON output only
- ✅ **Dependencies**: Minimal, well-established packages only

### 2. config.json Requirements

#### Complete Structure
```json
{
  "id": "utility-name",
  "name": "Human Readable Name",
  "version": "1.0.0",
  "description": "Brief description of utility function",
  "author": "FileDuck Team",
  "category": "File Conversion|Data Processing|File Management",
  "tags": ["tag1", "tag2", "tag3"],
  "entry": "main.py",
  "language": "python",
  "requirements": [
    "package-name>=version"
  ],
  "ui": "INLINE_JSX_STRING_HERE"
}
```

#### UI Field Specification
The `ui` field must contain a complete React component as a single string:

```javascript
// Example UI structure (as string in JSON)
"ui": "<div className=\"space-y-6\"><div className=\"text-center\"><h1 className=\"text-2xl font-bold text-gray-900\">Utility Name</h1><p className=\"text-gray-600 mt-2\">Brief description</p></div><div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\"><Card><CardHeader><CardTitle className=\"flex items-center gap-2\"><Upload className=\"h-5 w-5 text-blue-600\" />Input Files</CardTitle><CardDescription>Select files to process</CardDescription></CardHeader><CardContent><FileDropZone onFilesChange={setFiles} accept=\".ext\" multiple={true} maxFiles={10} className=\"border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors\" /></CardContent></Card><Card><CardHeader><CardTitle className=\"flex items-center gap-2\"><Download className=\"h-5 w-5 text-green-600\" />Processed Files</CardTitle><CardDescription>Results will appear here</CardDescription></CardHeader><CardContent><ConvertedFilesDisplay files={convertedFiles} onOpenFolder={handleOpenFolder} className=\"min-h-[200px]\" /></CardContent></Card></div><ConversionProgress isConverting={isConverting} progress={conversionProgress} error={error} className=\"my-4\" /><div className=\"flex justify-center\"><Button onClick={handleConvert} disabled={files.length === 0 || isConverting} size=\"lg\" className=\"px-8 bg-blue-600 hover:bg-blue-700 text-white\">{isConverting ? (<><div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>Processing...</>) : (<><FileText className=\"h-4 w-4 mr-2\" />Process Files</>)}</Button></div></div>"
```

### 3. Available UI Components

#### Core Components (Always Available)
```javascript
// Layout & Structure
<Card>, <CardHeader>, <CardTitle>, <CardDescription>, <CardContent>

// File Handling
<FileDropZone 
  onFilesChange={setFiles} 
  accept=".ext,.ext2" 
  multiple={true|false} 
  maxFiles={number}
  className="styling"
/>

<ConvertedFilesDisplay 
  files={convertedFiles} 
  onOpenFolder={handleOpenFolder}
  className="styling"
/>

// Progress & Feedback
<ConversionProgress 
  isConverting={isConverting} 
  progress={conversionProgress} 
  error={error}
  className="styling"
/>

// Interactive Elements
<Button 
  onClick={handleFunction} 
  disabled={condition} 
  size="sm|md|lg"
  className="styling"
>
  Content
</Button>

<Select>, <SelectTrigger>, <SelectValue>, <SelectContent>, <SelectItem>
<Switch>, <Label>

// Icons (Lucide React)
<Upload />, <Download />, <Settings />, <FileText />, <File />
```

#### Required State Variables
```javascript
// These variables are automatically available in your UI:
const [files, setFiles] = useState([]);
const [convertedFiles, setConvertedFiles] = useState([]);
const [isConverting, setIsConverting] = useState(false);
const [conversionProgress, setConversionProgress] = useState(0);
const [error, setError] = useState(null);
const [formData, setFormData] = useState({});

// Required functions:
const handleConvert = () => { /* Conversion logic */ };
const handleOpenFolder = () => { /* Open converted folder */ };
```

### 4. Apple Design Guidelines Integration

#### Typography Classes
```css
/* Headers */
.text-2xl.font-bold.text-gray-900     /* Main titles */
.text-xl.font-medium.text-gray-900    /* Section headers */

/* Body Text */
.text-gray-600                        /* Secondary text */
.text-gray-500                        /* Tertiary text */

/* Interactive States */
.hover:border-blue-400.transition-colors  /* Hover effects */
.bg-blue-600.hover:bg-blue-700           /* Button states */
```

#### Layout Patterns
```css
/* Spacing */
.space-y-6                           /* Vertical spacing */
.gap-6                              /* Grid gaps */
.p-6, .px-8, .py-4                  /* Padding */

/* Grid Layouts */
.grid.grid-cols-1.lg:grid-cols-2.gap-6  /* Responsive 2-column */

/* Cards & Surfaces */
.border-2.border-dashed.border-gray-300  /* Dropzone styling */
.bg-blue-50.border.border-blue-200       /* Progress containers */
```

### 5. Testing Requirements (tests.py)

```python
import unittest
import json
import tempfile
import os
from main import main_function

class TestUtility(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        
    def test_valid_input(self):
        """Test with valid input files"""
        # Test implementation
        pass
        
    def test_missing_file(self):
        """Test handling of missing files"""
        # Test implementation
        pass
        
    def test_invalid_file_format(self):
        """Test handling of invalid/corrupt files"""
        # Test implementation
        pass
        
    def tearDown(self):
        """Clean up test fixtures"""
        # Cleanup code
        pass

if __name__ == '__main__':
    unittest.main()
```

### 6. requirements.txt Format

```txt
# Core dependencies only
pandas>=1.5.0
requests>=2.28.0
# No version pinning unless absolutely necessary
# One package per line
# Include only essential packages
```

## 🚀 Development Workflow

### Step 1: Plan & Design
1. Choose utility type and core functionality
2. Identify required Python packages (minimal set)
3. Design UI layout following Apple guidelines
4. Plan file input/output workflow

### Step 2: Implement Python Logic
1. Create `main.py` with proper structure
2. Implement core functionality with error handling
3. Test CLI interface with sample JSON input
4. Ensure output goes to `/converted/` directory

### Step 3: Design UI
1. Create inline JSX using available components
2. Follow 2-column layout pattern (Input | Output)
3. Add settings/configuration options if needed
4. Include proper loading states and error handling

### Step 4: Test & Package
1. Write comprehensive unit tests
2. Create `requirements.txt` with minimal dependencies
3. Validate `config.json` syntax
4. Package into `.zip` file

### Step 5: Integration Test
1. Install in FileDuck application
2. Test full workflow: install → run → output
3. Verify error handling and edge cases
4. Confirm Apple design compliance

## ⚠️ Critical Requirements

### MUST DO
- ✅ Use spawn-based Python execution (already implemented in FileDuck)
- ✅ Follow exact file structure and naming
- ✅ Use only existing FileDuck components (no custom shortcuts)
- ✅ Implement proper error handling with JSON output
- ✅ Save outputs to `/converted/` directory
- ✅ Include comprehensive unit tests
- ✅ Follow Apple design guidelines exactly

### MUST NOT DO
- ❌ Use GUI prompts or `input()` calls in Python
- ❌ Create custom UI components or shortcuts
- ❌ Embed file content in JSON (use file paths only)
- ❌ Use excessive or unnecessary dependencies
- ❌ Ignore error handling or edge cases
- ❌ Deviate from the specified file structure

## 📝 Example Prompt for Cursor

```
Build a [UTILITY_TYPE] utility for FileDuck following fileduck-sdk-design-guideline.md

Requirements:
- Create complete utility in /apps/[utility-name]/dist/ folder
- Follow exact Python structure with stdin/stdout JSON communication
- Use inline JSX with existing FileDuck components only
- Include comprehensive tests and minimal requirements.txt
- Follow Apple design guidelines for UI
- Save outputs to /converted/ directory
- Package as final .zip file

The utility should [SPECIFIC_FUNCTIONALITY_DESCRIPTION].
```

---

**This guideline ensures consistent, high-quality FileDuck utilities that integrate seamlessly with the existing application architecture.**
