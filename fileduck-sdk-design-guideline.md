# 🦆 FileDuck SDK Design Guideline v2.1

## 🎯 Quick Reference

**Build FileDuck utilities with**: Python 3.x + React JSX + Apple Design + ShadCN Components

**Structure**: `/apps/{name}/dist/` → `main.py` + `config.json` + `tests.py` + `requirements.txt` → `.zip`

**Communication**: Python CLI ↔ JSON stdin/stdout ↔ FileDuck Electron

## 🏗️ Required File Structure
```
/apps/{utility-name}/
├── dist/
│   ├── main.py           # Python logic (CLI + JSON I/O)
│   ├── config.json       # Metadata + inline JSX UI
│   ├── tests.py          # Unit tests (3+ test cases)
│   └── requirements.txt  # Minimal Python deps
└── {utility-name}.zip    # Distribution package
```

## 📋 Core Files

### 1. main.py Template
```python
#!/usr/bin/env python3
"""
Utility Name - Brief Description
"""
import sys
import json
import os
from pathlib import Path

def main_function(input_data, config_options=None):
    """
    Core utility function
    
    Args:
        input_data: File data from FileDuck
        config_options: User configuration options
    
    Returns:
        dict: Result with success status and output info
    """
    try:
        # Implementation logic here
        
        # Create output in /converted/ directory
        converted_dir = Path.cwd() / "converted"
        converted_dir.mkdir(exist_ok=True)
        
        return {
            'success': True,
            'output_file': str(output_path),
            'message': 'Operation completed successfully'
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'message': f'Operation failed: {str(e)}'
        }

def main():
    """Main entry point for FileDuck communication"""
    try:
        # Read input from stdin (JSON format)
        input_data = json.load(sys.stdin)
        
        files = input_data.get('files', [])
        config = input_data.get('config', {})
        
        if not files:
            result = {
                'success': False,
                'error': 'No files provided',
                'message': 'Please select files to process'
            }
            print(json.dumps(result))
            return
        
        results = []
        for file_info in files:
            file_path = file_info.get('path') or file_info.get('filePath')
            if file_path:
                result = main_function(file_path, config)
                results.append({
                    'input_file': file_path,
                    'result': result
                })
        
        output = {
            'success': True,
            'results': results,
            'total_files': len(results),
            'successful_operations': sum(1 for r in results if r['result']['success'])
        }
        
        print(json.dumps(output))
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'message': f'Operation failed: {str(e)}'
        }
        print(json.dumps(error_result))

if __name__ == '__main__':
    main()
```

**Key Rules**: CLI-only • JSON I/O • File paths only • Save to `/converted/` • Minimal deps

**Input Format**: FileDuck sends JSON via stdin:
```json
{
  "files": [{"path": "/full/path/to/file.ext", "filePath": "/alt/path"}],
  "config": {"outputOptions": {"setting": "value"}}
}
```

**Output Format**: Always return JSON via stdout:
```json
{
  "success": true,
  "results": [{"input_file": "path", "result": {"success": true, "output_file": "path"}}],
  "total_files": 1,
  "successful_operations": 1
}
```

### 2. config.json Structure
```json
{
  "id": "utility-name",
  "name": "Human Readable Name",
  "version": "1.0.0",
  "description": "Brief description of utility function",
  "author": "FileDuck Team",
  "category": "File Conversion|Data Processing|File Management",
  "tags": ["tag1", "tag2", "tag3"],
  "entry": "main.py",
  "language": "python",
  "requirements": [
    "package-name>=version"
  ],
  "ui": "INLINE_JSX_STRING_HERE"
}
```

#### UI Field Specification
The `ui` field must contain a complete React component as a single string:

```javascript
// Example UI structure (as string in JSON)
"ui": "<div className=\"space-y-6\"><div className=\"text-center\"><h1 className=\"text-2xl font-bold text-gray-900\">Utility Name</h1><p className=\"text-gray-600 mt-2\">Brief description</p></div><div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\"><Card><CardHeader><CardTitle className=\"flex items-center gap-2\"><Upload className=\"h-5 w-5 text-blue-600\" />Input Files</CardTitle><CardDescription>Select files to process</CardDescription></CardHeader><CardContent><FileDropZone onFilesChange={setFiles} accept=\".ext\" multiple={true} maxFiles={10} className=\"border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors\" /></CardContent></Card><Card><CardHeader><CardTitle className=\"flex items-center gap-2\"><Download className=\"h-5 w-5 text-green-600\" />Processed Files</CardTitle><CardDescription>Results will appear here</CardDescription></CardHeader><CardContent><ConvertedFilesDisplay files={convertedFiles} onOpenFolder={handleOpenFolder} className=\"min-h-[200px]\" /></CardContent></Card></div><ConversionProgress isConverting={isConverting} progress={conversionProgress} error={error} className=\"my-4\" /><div className=\"flex justify-center\"><Button onClick={handleConvert} disabled={files.length === 0 || isConverting} size=\"lg\" className=\"px-8 bg-blue-600 hover:bg-blue-700 text-white\">{isConverting ? (<><div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>Processing...</>) : (<><FileText className=\"h-4 w-4 mr-2\" />Process Files</>)}</Button></div></div>"
```

### 3. Available UI Components

#### Core Components (Always Available)
```javascript
// Layout & Structure
<Card>, <CardHeader>, <CardTitle>, <CardDescription>, <CardContent>

// File Handling
<FileDropZone 
  onFilesChange={setFiles} 
  accept=".ext,.ext2" 
  multiple={true|false} 
  maxFiles={number}
  className="styling"
/>

<ConvertedFilesDisplay 
  files={convertedFiles} 
  onOpenFolder={handleOpenFolder}
  className="styling"
/>

// Progress & Feedback
<ConversionProgress 
  isConverting={isConverting} 
  progress={conversionProgress} 
  error={error}
  className="styling"
/>

// Interactive Elements
<Button 
  onClick={handleFunction} 
  disabled={condition} 
  size="sm|md|lg"
  className="styling"
>
  Content
</Button>

<Select>, <SelectTrigger>, <SelectValue>, <SelectContent>, <SelectItem>
<Switch>, <Label>

// Icons (Lucide React)
<Upload />, <Download />, <Settings />, <FileText />, <File />
```

#### Auto-Available State & Functions
```javascript
// FileDuck automatically provides these in your UI context:
const [files, setFiles] = useState([]);           // Selected input files
const [convertedFiles, setConvertedFiles] = useState([]); // Output files
const [isConverting, setIsConverting] = useState(false);  // Processing state
const [conversionProgress, setConversionProgress] = useState(0); // 0-100
const [error, setError] = useState(null);         // Error message
const [formData, setFormData] = useState({});     // Settings form data

// Auto-available functions:
const handleConvert = () => { /* Calls your Python script */ };
const handleOpenFolder = () => { /* Opens /converted/ folder */ };
```

#### Development Testing
```bash
# Test Python syntax (dependencies may not be installed)
python3 -c "import ast; ast.parse(open('main.py').read()); print('Syntax OK')"

# Test JSON validity
python3 -c "import json; json.load(open('config.json')); print('JSON OK')"

# Test with sample input
echo '{"files":[{"path":"test.csv"}],"config":{}}' | python3 main.py
```

### 4. Apple Design Guidelines Integration

#### Typography Classes
```css
/* Headers */
.text-2xl.font-bold.text-gray-900     /* Main titles */
.text-xl.font-medium.text-gray-900    /* Section headers */

/* Body Text */
.text-gray-600                        /* Secondary text */
.text-gray-500                        /* Tertiary text */

/* Interactive States */
.hover:border-blue-400.transition-colors  /* Hover effects */
.bg-blue-600.hover:bg-blue-700           /* Button states */
```

#### Layout Patterns
```css
/* Spacing */
.space-y-6                           /* Vertical spacing */
.gap-6                              /* Grid gaps */
.p-6, .px-8, .py-4                  /* Padding */

/* Grid Layouts */
.grid.grid-cols-1.lg:grid-cols-2.gap-6  /* Responsive 2-column */

/* Cards & Surfaces */
.border-2.border-dashed.border-gray-300  /* Dropzone styling */
.bg-blue-50.border.border-blue-200       /* Progress containers */
```

### 5. Testing Requirements (tests.py)

```python
import unittest
import json
import tempfile
import os
from main import main_function

class TestUtility(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        
    def test_valid_input(self):
        """Test with valid input files"""
        # Test implementation
        pass
        
    def test_missing_file(self):
        """Test handling of missing files"""
        # Test implementation
        pass
        
    def test_invalid_file_format(self):
        """Test handling of invalid/corrupt files"""
        # Test implementation
        pass
        
    def tearDown(self):
        """Clean up test fixtures"""
        # Cleanup code
        pass

if __name__ == '__main__':
    unittest.main()
```

### 6. requirements.txt Format

```txt
# Core dependencies only
pandas>=1.5.0
requests>=2.28.0
# No version pinning unless absolutely necessary
# One package per line
# Include only essential packages
```

## 🚀 Development Workflow

### Step 1: Plan & Design
1. Choose utility type and core functionality
2. Identify required Python packages (minimal set)
3. Design UI layout following Apple guidelines
4. Plan file input/output workflow

### Step 2: Implement Python Logic
1. Create `main.py` with proper structure
2. Implement core functionality with error handling
3. Test CLI interface with sample JSON input
4. Ensure output goes to `/converted/` directory

### Step 3: Design UI
1. Create inline JSX using available components
2. Follow 2-column layout pattern (Input | Output)
3. Add settings/configuration options if needed
4. Include proper loading states and error handling

### Step 4: Test & Package
1. Write comprehensive unit tests
2. Create `requirements.txt` with minimal dependencies
3. Validate `config.json` syntax
4. Package into `.zip` file

### Step 5: Integration Test
1. Install in FileDuck application
2. Test full workflow: install → run → output
3. Verify error handling and edge cases
4. Confirm Apple design compliance

## ⚠️ Critical Requirements

### MUST DO
- ✅ Use spawn-based Python execution (already implemented in FileDuck)
- ✅ Follow exact file structure and naming
- ✅ Use only existing FileDuck components (no custom shortcuts)
- ✅ Implement proper error handling with JSON output
- ✅ Save outputs to `/converted/` directory
- ✅ Include comprehensive unit tests
- ✅ Follow Apple design guidelines exactly

### MUST NOT DO
- ❌ Use GUI prompts or `input()` calls in Python
- ❌ Create custom UI components or shortcuts
- ❌ Embed file content in JSON (use file paths only)
- ❌ Use excessive or unnecessary dependencies
- ❌ Ignore error handling or edge cases
- ❌ Deviate from the specified file structure

## 📝 Example Prompt for Cursor

```
Build a [UTILITY_TYPE] utility for FileDuck following fileduck-sdk-design-guideline.md

Requirements:
- Create complete utility in /apps/[utility-name]/dist/ folder
- Follow exact Python structure with stdin/stdout JSON communication
- Use inline JSX with existing FileDuck components only
- Include comprehensive tests and minimal requirements.txt
- Follow Apple design guidelines for UI
- Save outputs to /converted/ directory
- Package as final .zip file

The utility should [SPECIFIC_FUNCTIONALITY_DESCRIPTION].
```

## 🎨 UI Design Patterns

### Standard Layout Structure
```javascript
// Recommended layout pattern for all utilities
<div className="space-y-6">
  {/* Header Section */}
  <div className="text-center">
    <h1 className="text-2xl font-bold text-gray-900">Utility Name</h1>
    <p className="text-gray-600 mt-2">Brief description</p>
  </div>

  {/* Main Content - 2 Column Grid */}
  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
    {/* Input Section */}
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5 text-blue-600" />
          Input Files
        </CardTitle>
        <CardDescription>Description of input</CardDescription>
      </CardHeader>
      <CardContent>
        <FileDropZone {...props} />
      </CardContent>
    </Card>

    {/* Output Section */}
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5 text-green-600" />
          Processed Files
        </CardTitle>
        <CardDescription>Results description</CardDescription>
      </CardHeader>
      <CardContent>
        <ConvertedFilesDisplay {...props} />
      </CardContent>
    </Card>
  </div>

  {/* Settings Section (Optional) */}
  <Card className="shadow-sm">
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <Settings className="h-5 w-5 text-purple-600" />
        Settings
      </CardTitle>
    </CardHeader>
    <CardContent>
      {/* Configuration options */}
    </CardContent>
  </Card>

  {/* Progress Section */}
  <ConversionProgress {...props} />

  {/* Action Button */}
  <div className="flex justify-center">
    <Button {...props}>Process Files</Button>
  </div>
</div>
```

### Color Scheme Standards
```css
/* Primary Actions */
.bg-blue-600.hover:bg-blue-700.text-white    /* Convert/Process buttons */

/* Status Colors */
.text-green-600                               /* Success states */
.text-red-600                                 /* Error states */
.text-yellow-600                              /* Warning states */
.text-purple-600                              /* Settings/config */

/* Icon Colors */
.text-blue-600                                /* Input icons */
.text-green-600                               /* Output icons */
.text-purple-600                              /* Settings icons */
.text-gray-400                                /* Neutral icons */
```

## 🔧 Common Utility Types & Examples

### File Converters
- **Input**: Single or multiple files of specific format
- **Output**: Converted files in target format
- **Settings**: Format options, quality settings
- **Examples**: PDF to Word, Image format conversion, Audio conversion

### Data Processors
- **Input**: Data files (CSV, JSON, XML)
- **Output**: Processed/cleaned data files
- **Settings**: Processing options, filters
- **Examples**: CSV cleaner, JSON formatter, Data merger

### File Utilities
- **Input**: Files for manipulation
- **Output**: Modified files
- **Settings**: Operation parameters
- **Examples**: File splitter, Batch renamer, Metadata editor

## 🐛 Error Handling Patterns

### Python Error Response Format
```python
# Success Response
{
    'success': True,
    'output_file': '/path/to/output',
    'message': 'Operation completed successfully',
    'metadata': {
        'processed_count': 5,
        'file_size': '2.3MB'
    }
}

# Error Response
{
    'success': False,
    'error': 'FileNotFoundError: Input file not found',
    'message': 'Failed to process file: file.pdf not found',
    'error_code': 'FILE_NOT_FOUND'
}
```

### UI Error Display
```javascript
// Error state in ConversionProgress component
<ConversionProgress
  isConverting={false}
  error="Failed to process file: Invalid format"
  className="my-4"
/>
```

## 📦 Distribution & Installation

### ZIP Package Structure
```
utility-name.zip
├── main.py
├── config.json
├── tests.py
├── requirements.txt
└── README.md (optional)
```

### Installation Flow
1. User downloads/selects ZIP file
2. FileDuck extracts to `/userData/utilities/{utility-name}/`
3. FileDuck reads `config.json` for metadata
4. FileDuck installs Python dependencies from `requirements.txt`
5. Utility appears in FileDuck interface
6. User can run utility through FileDuck UI

## 🎯 Quality Checklist

### Before Packaging
- [ ] Python code follows CLI-only pattern
- [ ] JSON communication works correctly
- [ ] All dependencies are minimal and necessary
- [ ] Unit tests cover main functionality and edge cases
- [ ] UI uses only existing FileDuck components
- [ ] Apple design guidelines are followed
- [ ] Error handling is comprehensive
- [ ] Output goes to `/converted/` directory
- [ ] config.json is valid JSON
- [ ] File structure matches specification exactly

### Testing Checklist
- [ ] Install utility in FileDuck successfully
- [ ] UI renders correctly with proper styling
- [ ] File upload/drag-drop works
- [ ] Processing completes successfully
- [ ] Error states display properly
- [ ] Output files are accessible
- [ ] "Open Folder" functionality works
- [ ] Uninstall removes all traces

---

**This comprehensive guideline ensures consistent, high-quality FileDuck utilities that integrate seamlessly with the existing application architecture and provide excellent user experience following Apple design principles.**
