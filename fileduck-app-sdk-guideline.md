
# 🦆 FileDuck Utility App SDK Guideline (`fileduck-app-sdk-guideline.md`)

## 🎯 Objective

This document defines a strict blueprint for AI agents (like Cursor) to auto-generate modular utility apps (e.g., file converters, cleaners, editors) under the **FileDuck SDK** framework.

All generated outputs must follow:
- Our **`apple-design-guideline.md`** for all UI styling and layout
- Our **standard app architecture** (main.py + config.json + UI + tests + zip)
- Minimalist, drag-and-drop-friendly UX
- Production-level quality

---

## 🧠 How the System Works

Given a prompt like:

> "Generate a CSV to JSON converter app using fileduck-app-sdk-guideline.md"

The AI must generate a full, working, Electron-compatible utility app with these components:

### 1. `main.py`
- Contains all core logic for the operation
- Error-handled, modular functions
- CLI-callable and callable from Electron via spawn
- Logging included for success, failure, and warnings
- Paths to input/output must be passed via CLI args

### 2. `config.json`
A structured metadata file defining the app module:

```json
{
  "id": "csv-to-json",
  "name": "CSV to JSON Converter",
  "version": "1.0.0",
  "description": "Convert CSV data to JSON using pandas.",
  "author": "FileDuck Team",
  "language": "python",
  "main": "main.py",
  "requirements": ["pandas"],
  "tags": ["csv", "json", "convert", "pandas"],
  "category": "Data Conversion",
  "icon": "icon.png",
  "ui": "<div className='flex gap-4'><div className='w-1/2'><Dropzone /></div><div className='flex flex-col justify-center'><ConvertButton /></div><div className='w-1/2'><OutputViewer /></div></div>"
}
```

🚨 **NOTE:** We **do not** use component shortcuts like `<AppleDropzone />`.  
You must write **full JSX React code** using atomic, Apple-styled, custom ShadCN components.  
You are building real UI — no abstract components allowed.

---

### 3. `ui` (React)
- Written as inline JSX string in config.json
- Should include:
  - Dropzone for input files
  - Convert button (with loading state)
  - Output preview box
  - Logs/warnings/errors below
- All components must follow our **Apple Design System**
- Do **not** include headers/footers — only body UI

---

### 4. `tests.py`
- Contains unit tests for `main.py`
- Tests for: correct output, file errors, edge cases
- Use `unittest` or `pytest` format

---

### 5. `requirements.txt`
- Only include minimum necessary dependencies
- Each package on a new line, no version pinning unless required

---

### 6. 📦 Output
A final `.zip` containing:
```
/main.py
/config.json
/tests.py
/requirements.txt
/icon.png (optional)
```

---

## 🧱 Design Guidelines for Each Component

### ✅ Python (`main.py`)
- Always accept CLI file paths as input/output args
- Use `argparse` or `sys.argv` safely
- Never use `input()` or GUI prompts
- Use `logging` module with clear messages
- Fail gracefully: raise clear errors

### ✅ UI (React inside config.json)
- No shortcuts or abstract components (e.g., `<AppleDropzone />` not allowed)
- Build using custom Apple-like ShadCN atomic components as we are using currently in our app
- the layout of the utility must sync with design of our electron app
- Layout: Dropzone → Button → Output → Status

### ✅ config.json
- Must contain all metadata for app registry and UI
- Must be valid JSON
- Inline JSX inside the `"ui"` field as a string

### ✅ Testing
- Write testable functions in `main.py`
- Include at least 3 test cases:
  - Valid input
  - Missing file
  - Corrupt/malformed data

---

## 🤖 Final Notes for Cursor

- Always follow **Apple Design Guidelines**
- UI is rendered inside FileDuck Electron webview
- Use file paths only — **never embed file content**
- Keep UI minimal, atomic, and focused on task UX

---

## 🔧 Prompt Example

> "Build a PDF page merger app using fileduck-app-sdk-guideline.md"

→ Cursor reads this guide and outputs a full zip package: `main.py`, `config.json` (with actual JSX UI), `requirements.txt`, `tests.py`, zipped and ready.

---
